{"name": "luxury-watches-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@medusajs/medusa-js": "^6.1.10", "@portabletext/react": "^3.2.1", "@sanity/cli": "^3.92.0", "@sanity/client": "^7.5.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.92.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/postcss": "^4.1.10", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.17.0", "lucide-react": "^0.514.0", "next": "15.3.3", "next-intl": "^4.1.0", "next-sanity": "^9.12.0", "next-seo": "^6.8.0", "postcss": "^8.5.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}