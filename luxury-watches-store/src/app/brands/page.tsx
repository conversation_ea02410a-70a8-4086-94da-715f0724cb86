import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowRight, Calendar, MapPin, Award } from 'lucide-react'
import { LUXURY_BRANDS } from '@/lib/constants'

export const metadata: Metadata = {
  title: 'Luxury Watch Brands - Luxury Watches Store',
  description: 'Discover the world\'s most prestigious luxury watch brands, their heritage, and iconic timepieces.',
}

const brandImages = {
  'rolex': 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=600&h=400&fit=crop&crop=center',
  'patek-philippe': 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=400&fit=crop&crop=center',
  'audemars-piguet': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=400&fit=crop&crop=center',
  'omega': 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=600&h=400&fit=crop&crop=center',
  'cartier': 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=600&h=400&fit=crop&crop=center',
  'breitling': 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=600&h=400&fit=crop&crop=center'
}

export default function BrandsPage() {
  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
              Prestigious <span className="text-luxury-gold">Brands</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Explore the heritage and craftsmanship of the world's most renowned luxury watch manufacturers
            </p>
          </div>
        </div>
      </section>

      {/* Brands Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {LUXURY_BRANDS.map((brand, index) => (
              <motion.div
                key={brand.slug}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={brandImages[brand.slug as keyof typeof brandImages] || brandImages.rolex}
                    alt={`${brand.name} luxury watches`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-4 left-4">
                    <h3 className="font-luxury-serif text-2xl font-bold text-white">
                      {brand.name}
                    </h3>
                  </div>
                </div>
                
                <div className="p-6">
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {brand.description}
                  </p>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Founded {brand.founded}
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      Switzerland
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-4">
                    <Award className="w-4 h-4 text-luxury-gold" />
                    <span className="text-sm text-luxury-gold font-medium">
                      Luxury Heritage Brand
                    </span>
                  </div>
                  
                  <Link
                    href={`/brands/${brand.slug}`}
                    className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark font-semibold group-hover:gap-3 transition-all"
                  >
                    Explore Brand
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
            Discover Your Perfect Timepiece
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Browse our curated collection of luxury watches from these prestigious brands
          </p>
          <Link
            href="/catalog"
            className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
          >
            View All Watches
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  )
}
