'use client';

import { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Filter, Grid, List, SlidersHorizontal, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { WatchCard } from '@/components/product/watch-card';
import { LUXURY_BRANDS, WATCH_CATEGORIES, WATCH_COLLECTIONS, PRICE_RANGES, SORT_OPTIONS } from '@/lib/constants';
import { getAllProducts } from '@/lib/products';
import { cn } from '@/lib/utils';



export default function CatalogPage() {
  const searchParams = useSearchParams();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>('');
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [sortBy, setSortBy] = useState('featured');

  // Handle URL search parameters
  useEffect(() => {
    const urlSearch = searchParams.get('search');
    const urlCollection = searchParams.get('collection');

    if (urlSearch) {
      setSearchQuery(decodeURIComponent(urlSearch));
    }

    if (urlCollection) {
      setSelectedCollection(urlCollection);
    }
  }, [searchParams]);

  // Filter and sort watches
  const filteredWatches = useMemo(() => {
    let filtered = getAllProducts();

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(watch =>
        watch.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        watch.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
        watch.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Brand filter
    if (selectedBrands.length > 0) {
      filtered = filtered.filter(watch => selectedBrands.includes(watch.brand));
    }

    // Category filter
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(watch => selectedCategories.includes(watch.category));
    }

    // Collection filter
    if (selectedCollection) {
      filtered = filtered.filter(watch => watch.collection === selectedCollection);
    }

    // Price range filter
    if (selectedPriceRange) {
      const range = PRICE_RANGES.find(r => r.label === selectedPriceRange);
      if (range) {
        filtered = filtered.filter(watch =>
          watch.price >= range.min && watch.price <= range.max
        );
      }
    }

    // Sort
    switch (sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'brand-asc':
        filtered.sort((a, b) => a.brand.localeCompare(b.brand));
        break;
      case 'brand-desc':
        filtered.sort((a, b) => b.brand.localeCompare(a.brand));
        break;
      case 'newest':
        filtered.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
        break;
      default:
        // Featured - bestsellers first
        filtered.sort((a, b) => (b.isBestseller ? 1 : 0) - (a.isBestseller ? 1 : 0));
    }

    return filtered;
  }, [searchQuery, selectedBrands, selectedCategories, selectedCollection, selectedPriceRange, sortBy]);

  const toggleBrand = (brand: string) => {
    setSelectedBrands(prev =>
      prev.includes(brand)
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    );
  };

  const toggleCategory = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const clearFilters = () => {
    setSelectedBrands([]);
    setSelectedCategories([]);
    setSelectedCollection('');
    setSelectedPriceRange('');
    setSearchQuery('');
  };

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Header */}
      <div className="bg-luxury-cream py-12">
        <div className="container mx-auto px-4">
          <h1 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black mb-4">
            {selectedCollection
              ? WATCH_COLLECTIONS.find(c => c.slug === selectedCollection)?.name || 'Watch Catalog'
              : 'Watch Catalog'
            }
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl">
            {selectedCollection
              ? WATCH_COLLECTIONS.find(c => c.slug === selectedCollection)?.description || 'Discover our exclusive collection of luxury watches from the world\'s leading brands'
              : 'Discover our exclusive collection of luxury watches from the world\'s leading brands'
            }
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className={cn(
            "lg:w-80 space-y-6",
            showFilters ? "block" : "hidden lg:block"
          )}>
            <div className="bg-white rounded-lg shadow-luxury p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-luxury-serif text-xl font-semibold">Filters</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-luxury-gold hover:text-luxury-gold-dark"
                >
                  Clear
                </Button>
              </div>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search watches..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
              </div>

              {/* Brands */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Brands</h4>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {LUXURY_BRANDS.slice(0, 8).map((brand) => (
                    <label key={brand.slug} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedBrands.includes(brand.name)}
                        onChange={() => toggleBrand(brand.name)}
                        className="rounded border-gray-300 text-luxury-gold focus:ring-luxury-gold"
                      />
                      <span className="ml-2 text-sm text-gray-700">{brand.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Categories */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Categories</h4>
                <div className="space-y-2">
                  {WATCH_CATEGORIES.map((category) => (
                    <label key={category.slug} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.slug)}
                        onChange={() => toggleCategory(category.slug)}
                        className="rounded border-gray-300 text-luxury-gold focus:ring-luxury-gold"
                      />
                      <span className="ml-2 text-sm text-gray-700">{category.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Collections */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Collections</h4>
                <div className="space-y-2">
                  {WATCH_COLLECTIONS.map((collection) => (
                    <label key={collection.slug} className="flex items-center">
                      <input
                        type="radio"
                        name="collection"
                        checked={selectedCollection === collection.slug}
                        onChange={() => setSelectedCollection(collection.slug)}
                        className="border-gray-300 text-luxury-gold focus:ring-luxury-gold"
                      />
                      <span className="ml-2 text-sm text-gray-700">{collection.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Price</h4>
                <div className="space-y-2">
                  {PRICE_RANGES.map((range) => (
                    <label key={range.label} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        checked={selectedPriceRange === range.label}
                        onChange={() => setSelectedPriceRange(range.label)}
                        className="border-gray-300 text-luxury-gold focus:ring-luxury-gold"
                      />
                      <span className="ml-2 text-sm text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
                <p className="text-gray-600">
                  Found {filteredWatches.length} watches
                </p>
              </div>

              <div className="flex items-center gap-4">
                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                >
                  {SORT_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                {/* View Mode */}
                <div className="flex border border-gray-300 rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <motion.div
              layout
              className={cn(
                "grid gap-6",
                viewMode === 'grid'
                  ? "grid-cols-1 md:grid-cols-2 xl:grid-cols-3"
                  : "grid-cols-1"
              )}
            >
              {filteredWatches.map((watch) => (
                <motion.div
                  key={watch.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <WatchCard watch={watch} viewMode={viewMode} />
                </motion.div>
              ))}
            </motion.div>

            {filteredWatches.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg mb-4">
                  No watches found matching your criteria
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
