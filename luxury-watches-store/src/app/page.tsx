import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-cream via-luxury-white to-luxury-cream"></div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-64 h-64 opacity-10">
          <div className="w-full h-full rounded-full border-4 border-luxury-gold"></div>
        </div>
        <div className="absolute bottom-20 left-20 w-32 h-32 opacity-10">
          <div className="w-full h-full rounded-full bg-luxury-gold"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 text-center">
          <h1 className="font-luxury-serif text-6xl md:text-8xl font-bold text-luxury-black mb-6 leading-tight">
            Luxury
            <span className="text-luxury-gold"> Watches</span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            Discover the finest luxury watches from the world's most prestigious brands
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button variant="luxury" size="xl" asChild>
              <Link href="/catalog">
                View Collection
              </Link>
            </Button>
            <Button variant="outline" size="xl" className="border-2 border-luxury-gold text-luxury-gold hover:bg-luxury-gold hover:text-luxury-black">
              Our Story
            </Button>
          </div>

          {/* Featured Watch Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="bg-white rounded-lg shadow-luxury p-6 hover:shadow-luxury-hover transition-all duration-300">
              <div className="w-full h-48 bg-luxury-cream rounded-lg mb-4 overflow-hidden">
                <img
                  src="/images/watches/rolex-submariner-1.webp"
                  alt="Rolex Submariner"
                  className="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-2">Rolex Submariner</h3>
              <p className="text-gray-600 text-sm">Iconic diving watch</p>
              <p className="text-luxury-gold font-bold text-lg mt-2">$12,500</p>
            </div>

            <div className="bg-white rounded-lg shadow-luxury p-6 hover:shadow-luxury-hover transition-all duration-300">
              <div className="w-full h-48 bg-luxury-cream rounded-lg mb-4 overflow-hidden">
                <img
                  src="/images/watches/IMG_8319_1e45f381-404f-4ec1-8a17-591c2f3c3ec9.webp"
                  alt="Patek Philippe Nautilus"
                  className="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-2">Patek Philippe Nautilus</h3>
              <p className="text-gray-600 text-sm">Luxury sports watch</p>
              <p className="text-luxury-gold font-bold text-lg mt-2">Price on Request</p>
            </div>

            <div className="bg-white rounded-lg shadow-luxury p-6 hover:shadow-luxury-hover transition-all duration-300">
              <div className="w-full h-48 bg-luxury-cream rounded-lg mb-4 overflow-hidden">
                <img
                  src="/images/watches/IMG_8320_ea6cd8c7-d3a8-4e88-b441-4aea8dae8f70.webp"
                  alt="Audemars Piguet Royal Oak"
                  className="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-2">Audemars Piguet Royal Oak</h3>
              <p className="text-gray-600 text-sm">Iconic octagonal design</p>
              <p className="text-luxury-gold font-bold text-lg mt-2">$28,900</p>
            </div>

            <div className="bg-white rounded-lg shadow-luxury p-6 hover:shadow-luxury-hover transition-all duration-300">
              <div className="w-full h-48 bg-luxury-cream rounded-lg mb-4 overflow-hidden">
                <img
                  src="/images/watches/IMG_8318_a52dd7c3-3bec-4bda-b92b-9381632b8846.webp"
                  alt="Luxury Watch"
                  className="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-2">Cartier Santos</h3>
              <p className="text-gray-600 text-sm">Aviation-inspired elegance</p>
              <p className="text-luxury-gold font-bold text-lg mt-2">$7,200</p>
            </div>
          </div>
        </div>
      </section>

      {/* Development Progress Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="font-luxury-serif text-4xl md:text-5xl font-bold text-center text-luxury-black mb-4">
            Development Stages
          </h2>
          <p className="text-center text-gray-600 mb-16 max-w-2xl mx-auto">
            Follow the progress of creating a luxury watch online store
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Phase 1 - Completed */}
            <div className="bg-luxury-cream p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 border-l-4 border-luxury-gold">
              <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-luxury-black font-bold text-xl">✓</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-luxury-black">Project Foundation</h3>
              <ul className="text-gray-700 space-y-3">
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Next.js 14 + TypeScript</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> TailwindCSS configured</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Project structure</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Base components</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Navigation and footer</li>
              </ul>
            </div>

            {/* Phase 2 - Completed */}
            <div className="bg-luxury-cream p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 border-l-4 border-luxury-gold">
              <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-luxury-black font-bold text-xl">✓</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-luxury-black">Product Catalog</h3>
              <ul className="text-gray-700 space-y-3">
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Watch cards</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Filtering</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Search</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> View modes</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Sorting</li>
              </ul>
            </div>

            {/* Phase 3 - Completed */}
            <div className="bg-luxury-cream p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 border-l-4 border-luxury-gold">
              <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-luxury-black font-bold text-xl">✓</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-luxury-black">Product Pages</h3>
              <ul className="text-gray-700 space-y-3">
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Detailed product pages</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Image gallery with zoom</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Specifications display</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Customer reviews</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Related products</li>
              </ul>
            </div>

            {/* Phase 4 - In Progress */}
            <div className="bg-yellow-50 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-l-4 border-yellow-400">
              <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-yellow-800 font-bold text-xl">4</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-gray-800">Cart & Checkout</h3>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-center"><span className="text-yellow-500 mr-2">⏳</span> Shopping cart</li>
                <li className="flex items-center"><span className="text-yellow-500 mr-2">⏳</span> Checkout process</li>
                <li className="flex items-center"><span className="text-yellow-500 mr-2">⏳</span> Stripe integration</li>
                <li className="flex items-center"><span className="text-yellow-500 mr-2">⏳</span> Order management</li>
                <li className="flex items-center"><span className="text-yellow-500 mr-2">⏳</span> Email notifications</li>
              </ul>
            </div>

            {/* Phase 5 - Pending */}
            <div className="bg-gray-50 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-l-4 border-gray-300">
              <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-gray-600 font-bold text-xl">5</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-gray-800">User Management</h3>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Registration/Login</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> User dashboard</li>
                <li className="flex items-center"><span className="text-green-500 mr-2">✅</span> Wishlist</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Order history</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Address book</li>
              </ul>
            </div>

            {/* Phase 6 - Pending */}
            <div className="bg-gray-50 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-l-4 border-gray-300">
              <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span className="text-gray-600 font-bold text-xl">6</span>
              </div>
              <h3 className="font-luxury-serif text-2xl font-semibold mb-4 text-gray-800">Final Touches</h3>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> SEO optimization</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Framer Motion animations</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Mobile optimization</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Testing</li>
                <li className="flex items-center"><span className="text-gray-400 mr-2">⏳</span> Deployment</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Brands Preview */}
      <section className="py-20 bg-luxury-charcoal text-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 opacity-5">
          <div className="w-full h-full rounded-full border-4 border-luxury-gold"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="font-luxury-serif text-4xl md:text-5xl font-bold mb-6 text-white">
            Prestigious Brands
          </h2>
          <p className="text-gray-300 mb-16 max-w-2xl mx-auto text-lg">
            We work only with the world's most prestigious watch brands
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { name: 'Rolex', description: 'A crown for every achievement', founded: '1905' },
              { name: 'Patek Philippe', description: 'You never actually own a Patek Philippe', founded: '1839' },
              { name: 'Audemars Piguet', description: 'To break the rules, you must first master them', founded: '1875' },
              { name: 'Omega', description: 'A legacy of precision', founded: '1848' }
            ].map((brand) => (
              <div key={brand.name} className="group p-8 bg-luxury-charcoal border border-gray-700 rounded-xl hover:border-luxury-gold transition-all duration-300 hover:shadow-luxury-hover">
                <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                  <span className="text-luxury-black font-bold text-xl">⌚</span>
                </div>
                <h3 className="font-luxury-serif text-2xl font-semibold text-luxury-gold mb-2">
                  {brand.name}
                </h3>
                <p className="text-gray-400 text-sm mb-2">
                  Founded in {brand.founded}
                </p>
                <p className="text-gray-300 text-sm italic">
                  "{brand.description}"
                </p>
              </div>
            ))}
          </div>

          <div className="mt-16">
            <Button variant="outline" size="lg" className="border-2 border-luxury-gold text-luxury-gold hover:bg-luxury-gold hover:text-luxury-black">
              View All Brands
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}