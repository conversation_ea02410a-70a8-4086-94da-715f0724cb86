import { Metadata } from 'next'
import { HeroSection } from '@/components/home/<USER>'
import { ProductShowcase, CategoryShowcase } from '@/components/home/<USER>'
import { TrustBadges, Testimonials, NewsletterSignup, SocialProofStats } from '@/components/home/<USER>'
import { BrandStory, HeritageTimeline } from '@/components/home/<USER>'
import { SITE_CONFIG } from '@/lib/constants'
import { getAllProducts } from '@/lib/sanity/utils'

export const metadata: Metadata = {
  title: `${SITE_CONFIG.name} - Premium Watches & Fine Jewelry`,
  description: SITE_CONFIG.description,
  keywords: [
    'luxury watches',
    'fine jewelry',
    'atlas luxury',
    'premium timepieces',
    'swiss watches',
    'diamond jewelry',
    'rolex',
    'patek philippe',
    'cartier',
    'luxury accessories'
  ],
}

// Sample data for showcases
const featuredWatches = [
  {
    id: '1',
    name: 'Submariner Date',
    brand: 'Rolex',
    price: 9550,
    image: '/images/rolex-submariner.jpg',
    category: 'diving',
    rating: 4.9,
    isNew: true
  },
  {
    id: '2',
    name: 'Nautilus',
    brand: 'Patek Philippe',
    price: 85000,
    image: '/images/patek-nautilus.jpg',
    category: 'luxury',
    rating: 5.0
  },
  {
    id: '3',
    name: 'Speedmaster Professional',
    brand: 'Omega',
    price: 6350,
    image: '/images/omega-speedmaster.jpg',
    category: 'chronograph',
    rating: 4.8
  }
]

const featuredJewelry = [
  {
    id: '4',
    name: 'Love Ring',
    brand: 'Cartier',
    price: 1850,
    image: '/images/cartier-love-ring.jpg',
    category: 'rings',
    rating: 4.9,
    isNew: true
  },
  {
    id: '5',
    name: 'Alhambra Necklace',
    brand: 'Van Cleef & Arpels',
    price: 4200,
    image: '/images/vca-alhambra.jpg',
    category: 'necklaces',
    rating: 5.0
  },
  {
    id: '6',
    name: 'B.zero1 Bracelet',
    brand: 'Bulgari',
    price: 2950,
    image: '/images/bulgari-bzero1.jpg',
    category: 'bracelets',
    rating: 4.8
  }
]

export default async function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />


      {/* Trust Badges */}
      <TrustBadges />

      {/* Category Showcase */}
      <CategoryShowcase />

      {/* Featured Watches */}
      <ProductShowcase
        title="Exceptional Timepieces"
        subtitle="Discover our curated selection of the world's finest luxury watches"
        products={featuredWatches}
        viewAllLink="/catalog?category=watches"
        type="watches"
      />

      {/* Social Proof Stats */}
      <SocialProofStats />

      {/* Featured Jewelry */}
      <ProductShowcase
        title="Exquisite Jewelry"
        subtitle="Explore our collection of fine jewelry crafted with precious metals and gemstones"
        products={featuredJewelry}
        viewAllLink="/catalog?category=jewelry"
        type="jewelry"
      />

      {/* Brand Story */}
      <BrandStory />

      {/* Testimonials */}
      <Testimonials />

      {/* Heritage Timeline */}
      <HeritageTimeline />

      {/* Newsletter Signup */}
      <NewsletterSignup />

    </div>
  )
}