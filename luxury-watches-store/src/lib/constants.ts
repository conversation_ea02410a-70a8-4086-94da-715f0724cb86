export const SITE_CONFIG = {
  name: 'Luxury Watches',
  description: 'Discover the finest luxury watches from the world\'s most prestigious brands',
  url: 'https://luxury-watches.com',
  ogImage: 'https://luxury-watches.com/og-image.jpg',
  contact: {
    address: '123 Fifth Avenue, New York, NY 10001',
    phone: '+****************',
    email: '<EMAIL>',
  },
  links: {
    instagram: 'https://instagram.com/luxurywatches',
    facebook: 'https://facebook.com/luxurywatches',
    twitter: 'https://twitter.com/luxurywatches',
  },
};

export const NAVIGATION_LINKS = [
  { name: 'Home', href: '/' },
  { name: 'Catalog', href: '/catalog' },
  { name: 'Brands', href: '/brands' },
  { name: 'Collections', href: '/collections' },
  { name: 'Blog', href: '/blog' },
  { name: 'About', href: '/about' },
  { name: '<PERSON>', href: '/contact' },
];

export const LUXURY_BRANDS = [
  { name: 'Rolex', slug: 'rolex', description: 'A crown for every achievement', founded: 1905 },
  { name: '<PERSON><PERSON>', slug: 'patek-philippe', description: 'You never actually own a Patek <PERSON>', founded: 1839 },
  { name: 'Audemars Piguet', slug: 'audemars-piguet', description: 'To break the rules, you must first master them', founded: 1875 },
  { name: 'Omega', slug: 'omega', description: 'A legacy of precision', founded: 1848 },
  { name: 'Cartier', slug: 'cartier', description: 'The jeweler of kings', founded: 1847 },
  { name: 'Jaeger-LeCoultre', slug: 'jaeger-lecoultre', description: 'The Grande Maison', founded: 1833 },
  { name: 'Vacheron Constantin', slug: 'vacheron-constantin', description: 'One of the oldest watch manufacturers', founded: 1755 },
  { name: 'IWC Schaffhausen', slug: 'iwc', description: 'Engineered for men', founded: 1868 },
  { name: 'Panerai', slug: 'panerai', description: 'Italian design meets Swiss precision', founded: 1860 },
  { name: 'Breitling', slug: 'breitling', description: 'Instruments for professionals', founded: 1884 },
];

export const WATCH_CATEGORIES = [
  { name: 'Dress Watches', slug: 'dress', description: 'Elegant watches for formal occasions' },
  { name: 'Sports Watches', slug: 'sports', description: 'Robust watches for active lifestyles' },
  { name: 'Diving Watches', slug: 'diving', description: 'Water-resistant watches for underwater adventures' },
  { name: 'Pilot Watches', slug: 'pilot', description: 'Aviation-inspired watches' },
  { name: 'Chronographs', slug: 'chronograph', description: 'Precision timing instruments' },
  { name: 'GMT Watches', slug: 'gmt', description: 'Multi-timezone watches for travelers' },
];

export const WATCH_COLLECTIONS = [
  { name: 'Vintage Classics', slug: 'vintage-classics', description: 'Timeless pieces that have defined horological excellence for decades' },
  { name: 'Modern Sports', slug: 'modern-sports', description: 'Contemporary timepieces designed for active lifestyles and adventure' },
  { name: 'Dress Elegance', slug: 'dress-elegance', description: 'Sophisticated timepieces perfect for formal occasions and business' },
  { name: 'Complications Masters', slug: 'complications-masters', description: 'Exceptional timepieces showcasing the pinnacle of watchmaking artistry' },
  { name: 'Limited Editions', slug: 'limited-editions', description: 'Exclusive timepieces with limited production runs for discerning collectors' },
  { name: "Women's Luxury", slug: 'womens-luxury', description: 'Exquisite timepieces designed specifically for the sophisticated woman' },
];

export const PRICE_RANGES = [
  { label: 'Under $5,000', min: 0, max: 5000 },
  { label: '$5,000 - $10,000', min: 5000, max: 10000 },
  { label: '$10,000 - $25,000', min: 10000, max: 25000 },
  { label: '$25,000 - $50,000', min: 25000, max: 50000 },
  { label: '$50,000 - $100,000', min: 50000, max: 100000 },
  { label: 'Over $100,000', min: 100000, max: Infinity },
];

export const SORT_OPTIONS = [
  { label: 'Featured', value: 'featured' },
  { label: 'Price: Low to High', value: 'price-asc' },
  { label: 'Price: High to Low', value: 'price-desc' },
  { label: 'Newest First', value: 'newest' },
  { label: 'Brand A-Z', value: 'brand-asc' },
  { label: 'Brand Z-A', value: 'brand-desc' },
];

export const FOOTER_LINKS = {
  shop: [
    { name: 'All Watches', href: '/catalog' },
    { name: 'New Arrivals', href: '/catalog?filter=new' },
    { name: 'Best Sellers', href: '/catalog?filter=bestsellers' },
    { name: 'Sale', href: '/catalog?filter=sale' },
    { name: 'Gift Cards', href: '/gift-cards' },
  ],
  brands: [
    { name: 'Rolex', href: '/brands/rolex' },
    { name: 'Patek Philippe', href: '/brands/patek-philippe' },
    { name: 'Audemars Piguet', href: '/brands/audemars-piguet' },
    { name: 'Omega', href: '/brands/omega' },
    { name: 'View All', href: '/brands' },
  ],
  support: [
    { name: 'Contact Us', href: '/contact' },
    { name: 'Size Guide', href: '/size-guide' },
    { name: 'Care Instructions', href: '/care' },
    { name: 'Warranty', href: '/warranty' },
    { name: 'Returns', href: '/returns' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press', href: '/press' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
};
